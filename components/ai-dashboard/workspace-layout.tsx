"use client";

import { GeneratorMain } from "./genrator-main";
import { useDeviceLayout } from "./hooks/use-device-layout";
import { useResponsiveStyles } from "./hooks/use-responsive-styles";
import {
  FullscreenToolbar,
  WorkspaceHeader,
  ModelTypeSelector,
  ModelTypeTabs,
  ResultCard,
  useWorkspaceState,
  useWorkspaceFullscreen
} from "./components";

export function WorkspaceLayout() {
  // 使用自定义hooks管理状态
  const {
    activeTab,
    generationResult,
    handleTabChange,
    setGenerationResult,
    setIsGenerating
  } = useWorkspaceState();

  const {
    isFullscreen,
    moduleRef,
    handleFullscreenToggle
  } = useWorkspaceFullscreen();

  const { isDesktop, isMobile } = useDeviceLayout();

  // 在组件顶层调用所有hooks，避免条件调用
  const { className: typeButtonClassName } = useResponsiveStyles({
    mobile: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-sm font-medium',
      width: 'w-full justify-start',
    },
    tablet: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-base font-medium',
      width: 'w-full justify-start',
    },
    desktop: {
      size: 'h-20 p-3',
      direction: 'flex-col',
      gap: 'gap-2',
      text: 'text-xs font-medium',
      width: 'w-full justify-center',
    },
  });

  return (
    <div
      ref={moduleRef}
      className={`
        ${isFullscreen
          ? "fixed inset-0 flex flex-col"
          : "container mx-auto max-w-6xl"
        }
        transition-all duration-300 ease-in-out
      `}
      style={isFullscreen ? {
        zIndex: 40,
        background: "#021e36ff",//'var(--background)',
        height: '100vh',
        maxHeight: '100vh',
        overflow: 'hidden'
      } : undefined}
    >
      {/* 全屏模式下的顶部工具栏 */}
      {isFullscreen && (
        <FullscreenToolbar onExitFullscreen={handleFullscreenToggle} />
      )}

      {/* 常规模式下的头部区域 */}
      {!isFullscreen && (
        <WorkspaceHeader onEnterFullscreen={handleFullscreenToggle} />
      )}

      {/* 主要内容区域 - 使用响应式容器 */}
      <div className={isFullscreen ? "px-6 pb-6 pt-4 flex-1 min-h-0 overflow-hidden" : "px-2 md:px-6 pb-12"}>
        <div className={
          isFullscreen
            ? isMobile
              ? "h-full flex flex-col gap-4 min-h-0"  // 移动端全屏：单栏布局，严格高度控制
              : "h-full grid grid-cols-10 gap-6 min-h-0"  // 桌面端全屏：多栏布局，严格高度控制
            : isMobile
              ? "flex flex-col gap-6"
              : "grid grid-cols-5 gap-6"
        } style={!isFullscreen && !isMobile ? { alignItems: 'stretch', gridAutoRows: '1fr' } : {}}>
          
          {/* 左栏：模型类型选择器 - 只在桌面端全屏时显示 */}
          {(isFullscreen && isDesktop && !isMobile) && (
            <div className="col-span-1 h-full p-4 bg-gradient-to-b from-muted/30 to-muted/10 rounded-2xl border border-border/30">
              <h3 className="text-sm font-semibold text-foreground mb-4 flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent"></div>
                MODEL TYPE
              </h3>
              <ModelTypeSelector
                activeTab={activeTab}
                onTabChange={handleTabChange}
                isFullscreen={isFullscreen}
                isDesktop={isDesktop}
                typeButtonClassName={typeButtonClassName}
              />
            </div>
          )}

          {/* 中栏：模型选择和参数设置 */}
          <div className={`${
            isFullscreen
              ? isMobile
                ? 'w-full min-h-0'  // 移动端全屏：占满宽度，限制高度
                : 'col-span-3 h-full min-h-0'  // 桌面端全屏：3栏，限制高度
              : isMobile
                ? 'w-full'
                : 'col-span-2'
          } flex flex-col ${isFullscreen ? 'overflow-hidden overflow-x-hidden' : ''}`}>
            {/* 移动端和非全屏桌面端显示水平标签页 */}
            {!(isFullscreen && isDesktop && !isMobile) && (
              <ModelTypeTabs
                activeTab={activeTab}
                onTabChange={handleTabChange}
              />
            )}

            {/* AI生成器组件 */}
            <div className={isFullscreen ? 'flex-1 min-h-0 overflow-y-auto overflow-x-hidden' : ''}>
              <GeneratorMain
                modelType={activeTab}
                onResultChange={setGenerationResult}
                onGeneratingChange={setIsGenerating}
              />
            </div>
          </div>

          {/* 右栏：结果展示 */}
          <div className={`${
            isFullscreen
              ? isMobile
                ? 'w-full flex-1 min-h-0 overflow-hidden'  // 移动端全屏：占满剩余空间，严格限制高度
                : 'col-span-6 h-full min-h-0 overflow-hidden'  // 桌面端全屏：6栏，严格限制高度
              : isMobile
                ? 'w-full mt-6'
                : 'col-span-3 h-full'
          }`}>
            <ResultCard
              activeTab={activeTab}
              generationResult={generationResult}
              isFullscreen={isFullscreen}
              isMobile={isMobile}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
