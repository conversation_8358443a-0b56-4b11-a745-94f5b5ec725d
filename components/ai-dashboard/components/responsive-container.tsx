"use client";

import React from 'react';
import { cn } from '@/lib/utils';
import { useContainerStyles, useResponsiveStyles, type ResponsiveStyleConfig } from '../hooks/use-responsive-styles';
import { useDeviceLayout } from '../hooks/use-device-layout';

// 布局模板类型
export type LayoutTemplate = 'three-column' | 'two-column' | 'single-column' | 'dashboard';

// 布局配置接口
export interface LayoutConfig {
  template: LayoutTemplate;
  fullscreen?: boolean;
  customStyles?: ResponsiveStyleConfig;
  className?: string;
}

// 预定义布局模板
const LAYOUT_TEMPLATES: Record<LayoutTemplate, ResponsiveStyleConfig> = {
  'three-column': {
    mobile: {
      display: 'flex flex-col',
      gap: 'gap-4',
      height: 'min-h-0',
    },
    tablet: {
      display: 'grid grid-cols-5',
      gap: 'gap-6',
      height: 'min-h-0',
    },
    desktop: {
      display: 'grid grid-cols-10',
      gap: 'gap-6',
      height: 'h-full min-h-0',
    },
  },
  'two-column': {
    mobile: {
      display: 'flex flex-col',
      gap: 'gap-4',
      height: 'min-h-0',
    },
    tablet: {
      display: 'grid grid-cols-5',
      gap: 'gap-6',
      height: 'min-h-0',
    },
    desktop: {
      display: 'grid grid-cols-5',
      gap: 'gap-6',
      height: 'h-full min-h-0',
    },
  },
  'single-column': {
    mobile: {
      display: 'flex flex-col',
      gap: 'gap-4',
      width: 'w-full',
    },
    tablet: {
      display: 'flex flex-col',
      gap: 'gap-6',
      width: 'w-full max-w-4xl mx-auto',
    },
    desktop: {
      display: 'flex flex-col',
      gap: 'gap-6',
      width: 'w-full max-w-6xl mx-auto',
    },
  },
  'dashboard': {
    mobile: {
      display: 'grid grid-cols-1',
      gap: 'gap-4',
    },
    tablet: {
      display: 'grid grid-cols-2',
      gap: 'gap-6',
    },
    desktop: {
      display: 'grid grid-cols-3',
      gap: 'gap-6',
    },
  },
};

// Section 组件接口
export interface SectionProps {
  name: string;
  children: React.ReactNode;
  className?: string;
  customStyles?: ResponsiveStyleConfig;
}

// Section 组件
export function Section({ name, children, className, customStyles }: SectionProps) {
  const { deviceType } = useDeviceLayout();

  // 根据 section 名称获取对应的样式模板
  const getSectionTemplate = (sectionName: string) => {
    switch (sectionName) {
      case 'sidebar':
      case 'left':
        return 'sidebar';
      case 'main':
      case 'center':
        return 'main';
      case 'result':
      case 'right':
        return 'result';
      default:
        return 'main';
    }
  };

  const templateName = getSectionTemplate(name);
  const { className: responsiveClassName } = useResponsiveStyles(
    customStyles || {},
    templateName as any
  );

  return (
    <div
      className={cn(responsiveClassName, className)}
      data-section={name}
    >
      {children}
    </div>
  );
}

// ResponsiveContainer 主组件接口
export interface ResponsiveContainerProps {
  layout: LayoutConfig;
  children: React.ReactNode;
  className?: string;
}

// ResponsiveContainer 主组件
export function ResponsiveContainer({ 
  layout, 
  children, 
  className 
}: ResponsiveContainerProps) {
  const { deviceType, isDesktop } = useDeviceLayout();
  
  // 获取布局模板样式
  const templateConfig = LAYOUT_TEMPLATES[layout.template];
  
  // 合并自定义样式
  const finalConfig = layout.customStyles 
    ? {
        mobile: { ...templateConfig.mobile, ...layout.customStyles.mobile },
        tablet: { ...templateConfig.tablet, ...layout.customStyles.tablet },
        desktop: { ...templateConfig.desktop, ...layout.customStyles.desktop },
      }
    : templateConfig;

  const { className: responsiveClassName } = useContainerStyles(finalConfig);

  // 全屏模式不需要额外的定位样式，因为外层容器已经处理了
  const fullscreenClassName = '';

  return (
    <div 
      className={cn(
        responsiveClassName,
        fullscreenClassName,
        layout.className,
        className,
        'transition-all duration-300 ease-in-out'
      )}
      data-layout={layout.template}
      data-device={deviceType}
      data-fullscreen={layout.fullscreen}
    >
      {children}
    </div>
  );
}

// 布局工厂函数 - 快速创建常用布局
export function createThreeColumnLayout(fullscreen = false): LayoutConfig {
  return {
    template: 'three-column',
    fullscreen,
  };
}

export function createTwoColumnLayout(fullscreen = false): LayoutConfig {
  return {
    template: 'two-column',
    fullscreen,
  };
}

export function createSingleColumnLayout(): LayoutConfig {
  return {
    template: 'single-column',
    fullscreen: false,
  };
}

export function createDashboardLayout(): LayoutConfig {
  return {
    template: 'dashboard',
    fullscreen: false,
  };
}
