# AI Dashboard 响应式布局框架

这是一个专为 AI Dashboard 设计的响应式布局框架，实现了关注点分离的架构，将布局逻辑、样式配置和业务逻辑完全分离。

## 🏗️ 架构概览

### 三层抽象架构

1. **布局容器层** (`ResponsiveContainer`)
   - 处理不同设备的容器布局
   - 支持预定义布局模板
   - 自动适配全屏/非全屏模式

2. **样式配置层** (`useResponsiveStyles`)
   - 设备特定的样式配置
   - 预定义样式模板
   - 动态样式计算

3. **设备检测层** (`useDeviceLayout`)
   - 实时设备类型检测
   - 屏幕尺寸监听
   - 响应式断点管理

## 📁 文件结构

```
components/ai-dashboard-branch/
├── hooks/
│   ├── use-device-layout.ts      # 设备检测 Hook
│   └── use-responsive-styles.ts  # 响应式样式 Hook
├── components/
│   └── responsive-container.tsx  # 响应式容器组件
├── config/
│   └── layout-configs.ts         # 布局配置文件
├── examples/
│   └── usage-examples.tsx        # 使用示例
├── genrator-main.tsx             # AI生成器组件（已重构）
├── workspace-layout.tsx          # 工作区布局组件（已重构）
└── README.md                     # 说明文档
```

## 🚀 快速开始

### 1. 基础使用

```tsx
import { ResponsiveContainer, Section } from './components/responsive-container';
import { getWorkspaceLayout, getSectionConfig } from './config/layout-configs';

function MyComponent() {
  const layout = getWorkspaceLayout(false); // 非全屏模式

  return (
    <ResponsiveContainer layout={layout}>
      <Section name="sidebar" customStyles={getSectionConfig('sidebar')}>
        {/* 侧边栏内容 */}
      </Section>
      
      <Section name="main" customStyles={getSectionConfig('main')}>
        {/* 主内容 */}
      </Section>
      
      <Section name="result" customStyles={getSectionConfig('result')}>
        {/* 结果区域 */}
      </Section>
    </ResponsiveContainer>
  );
}
```

### 2. 自定义响应式样式

```tsx
import { useResponsiveStyles } from './hooks/use-responsive-styles';

function CustomComponent() {
  const { className } = useResponsiveStyles({
    mobile: {
      width: 'w-full',
      padding: 'p-4',
      text: 'text-sm',
    },
    tablet: {
      width: 'w-1/2',
      padding: 'p-6',
      text: 'text-base',
    },
    desktop: {
      width: 'w-1/3',
      padding: 'p-8',
      text: 'text-lg',
    },
  });

  return <div className={className}>响应式内容</div>;
}
```

### 3. 设备检测

```tsx
import { useDeviceLayout } from './hooks/use-device-layout';

function DeviceAwareComponent() {
  const { deviceType, isMobile, isDesktop } = useDeviceLayout();

  return (
    <div>
      <p>当前设备: {deviceType}</p>
      {isMobile && <MobileSpecificComponent />}
      {isDesktop && <DesktopSpecificComponent />}
    </div>
  );
}
```

## 🎨 预定义布局模板

### 布局模板

- **three-column**: 三栏布局（PC端），Tab布局（移动端）
- **two-column**: 两栏布局（PC端），垂直布局（移动端）
- **single-column**: 单栏布局（全设备通用）
- **dashboard**: 仪表板布局（网格布局）

### 样式模板

- **container**: 容器样式
- **sidebar**: 侧边栏样式
- **main**: 主内容区样式
- **result**: 结果区域样式
- **card**: 卡片样式
- **button**: 按钮样式

## 📱 响应式断点

```typescript
const BREAKPOINTS = {
  mobile: 768,   // < 768px
  tablet: 1200,  // 768px - 1199px
  desktop: 1200, // >= 1200px
};
```

## 🔧 扩展新功能

### 1. 添加新的布局模板

```typescript
// 在 config/layout-configs.ts 中添加
export const NEW_FEATURE_LAYOUT = {
  template: 'custom' as const,
  fullscreen: false,
  customStyles: {
    mobile: { /* 移动端样式 */ },
    tablet: { /* 平板样式 */ },
    desktop: { /* 桌面端样式 */ },
  },
} as LayoutConfig;
```

### 2. 创建新的样式模板

```typescript
// 在 hooks/use-responsive-styles.ts 中添加
export const NEW_STYLE_TEMPLATE = {
  mobile: { /* 移动端样式 */ },
  tablet: { /* 平板样式 */ },
  desktop: { /* 桌面端样式 */ },
};
```

### 3. 使用新模板

```tsx
function NewFeatureComponent() {
  const { className } = useResponsiveStyles({}, 'newTemplate');
  
  return (
    <ResponsiveContainer layout={NEW_FEATURE_LAYOUT}>
      {/* 组件内容 */}
    </ResponsiveContainer>
  );
}
```

## ✨ 优势特性

### 🎯 关注点分离
- 布局逻辑与业务逻辑完全分离
- 样式配置统一管理
- 组件职责单一明确

### 📱 完美适配
- 自动适配移动端、平板、桌面端
- 流畅的响应式过渡
- 优化的触摸交互

### 🔄 高度复用
- 预定义模板快速开发
- 样式配置可复用
- 组件间一致的行为

### 🚀 易于扩展
- 渐进式架构设计
- 新功能快速集成
- 向后兼容保证

### 🛠️ 开发友好
- TypeScript 类型安全
- 清晰的API设计
- 丰富的使用示例

## 📋 最佳实践

1. **优先使用预定义模板** - 减少重复代码，保持一致性
2. **合理使用自定义样式** - 只在必要时创建，避免过度定制
3. **测试多设备表现** - 确保在不同设备上的良好体验
4. **保持配置简洁** - 避免过于复杂的样式配置
5. **遵循命名约定** - 使用语义化的section名称

## 🔄 迁移指南

从旧的硬编码样式迁移到新框架：

1. 识别组件中的布局逻辑
2. 选择合适的布局模板
3. 提取样式配置到配置文件
4. 使用ResponsiveContainer包装组件
5. 应用Section组件分离区域
6. 测试响应式表现

## 🤝 贡献指南

1. 新增布局模板需要同时支持三种设备类型
2. 样式配置应该语义化且易于理解
3. 所有新功能需要提供使用示例
4. 保持向后兼容性
5. 遵循现有的代码风格
