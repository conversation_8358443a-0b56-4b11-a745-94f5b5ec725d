/**
 * 响应式布局框架使用示例
 * 
 * 这个文件展示了如何使用新的响应式布局框架来创建适配不同设备的组件
 */

"use client";

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

// 导入响应式布局框架
import { ResponsiveContainer, Section } from '../components/responsive-container';
import { useDeviceLayout } from '../hooks/use-device-layout';
import { useResponsiveStyles } from '../hooks/use-responsive-styles';
import { AI_DASHBOARD_LAYOUTS, getSectionConfig } from '../config/layout-configs';

// 示例1：基础三栏布局
export function BasicThreeColumnExample() {
  const { isDesktop } = useDeviceLayout();
  
  return (
    <ResponsiveContainer layout={AI_DASHBOARD_LAYOUTS.workspaceNormal}>
      {/* 侧边栏 */}
      <Section name="sidebar" customStyles={getSectionConfig('sidebar')}>
        <Card>
          <CardHeader>
            <CardTitle>侧边栏</CardTitle>
          </CardHeader>
          <CardContent>
            <p>这是侧边栏内容</p>
          </CardContent>
        </Card>
      </Section>

      {/* 主内容区 */}
      <Section name="main" customStyles={getSectionConfig('main')}>
        <Card>
          <CardHeader>
            <CardTitle>主内容区</CardTitle>
          </CardHeader>
          <CardContent>
            <p>这是主内容区</p>
          </CardContent>
        </Card>
      </Section>

      {/* 结果区 */}
      <Section name="result" customStyles={getSectionConfig('result')}>
        <Card>
          <CardHeader>
            <CardTitle>结果区</CardTitle>
          </CardHeader>
          <CardContent>
            <p>这是结果展示区</p>
          </CardContent>
        </Card>
      </Section>
    </ResponsiveContainer>
  );
}

// 示例2：自定义响应式样式
export function CustomStylesExample() {
  const { className: customButtonStyles } = useResponsiveStyles({
    mobile: {
      size: 'h-10 px-3',
      text: 'text-sm',
      background: 'bg-blue-500',
    },
    tablet: {
      size: 'h-12 px-4',
      text: 'text-base',
      background: 'bg-green-500',
    },
    desktop: {
      size: 'h-14 px-6',
      text: 'text-lg',
      background: 'bg-purple-500',
    },
  });

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-4">自定义响应式按钮</h2>
      <Button className={`${customButtonStyles} text-white`}>
        响应式按钮
      </Button>
    </div>
  );
}

// 示例3：设备检测使用
export function DeviceDetectionExample() {
  const { deviceType, isMobile, isTablet, isDesktop, screenWidth } = useDeviceLayout();

  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-4">设备检测信息</h2>
      <div className="space-y-2">
        <p>设备类型: {deviceType}</p>
        <p>屏幕宽度: {screenWidth}px</p>
        <p>是否移动端: {isMobile ? '是' : '否'}</p>
        <p>是否平板: {isTablet ? '是' : '否'}</p>
        <p>是否桌面端: {isDesktop ? '是' : '否'}</p>
      </div>
    </div>
  );
}

// 示例4：全屏布局
export function FullscreenLayoutExample() {
  const [isFullscreen, setIsFullscreen] = React.useState(false);

  const layout = isFullscreen 
    ? AI_DASHBOARD_LAYOUTS.workspaceFullscreen 
    : AI_DASHBOARD_LAYOUTS.workspaceNormal;

  return (
    <div>
      <div className="p-6">
        <Button onClick={() => setIsFullscreen(!isFullscreen)}>
          {isFullscreen ? '退出全屏' : '进入全屏'}
        </Button>
      </div>
      
      <ResponsiveContainer layout={layout}>
        <Section name="sidebar">
          <Card>
            <CardContent className="p-4">
              <p>侧边栏 - {isFullscreen ? '全屏模式' : '普通模式'}</p>
            </CardContent>
          </Card>
        </Section>

        <Section name="main">
          <Card>
            <CardContent className="p-4">
              <p>主内容 - {isFullscreen ? '全屏模式' : '普通模式'}</p>
            </CardContent>
          </Card>
        </Section>

        <Section name="result">
          <Card>
            <CardContent className="p-4">
              <p>结果区 - {isFullscreen ? '全屏模式' : '普通模式'}</p>
            </CardContent>
          </Card>
        </Section>
      </ResponsiveContainer>
    </div>
  );
}

// 示例5：新功能扩展示例
export function NewFeatureExample() {
  // 为新功能定义自定义布局配置
  const customLayout = {
    template: 'dashboard' as const,
    fullscreen: false,
    customStyles: {
      mobile: {
        display: 'grid grid-cols-1',
        gap: 'gap-4',
        padding: 'p-4',
      },
      tablet: {
        display: 'grid grid-cols-2',
        gap: 'gap-6',
        padding: 'p-6',
      },
      desktop: {
        display: 'grid grid-cols-4',
        gap: 'gap-8',
        padding: 'p-8',
      },
    },
  };

  return (
    <ResponsiveContainer layout={customLayout}>
      {[1, 2, 3, 4].map((item) => (
        <Section key={item} name={`item-${item}`}>
          <Card>
            <CardHeader>
              <CardTitle>功能 {item}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>这是新功能 {item} 的内容</p>
            </CardContent>
          </Card>
        </Section>
      ))}
    </ResponsiveContainer>
  );
}

/**
 * 使用指南：
 * 
 * 1. 基础布局：
 *    - 使用 ResponsiveContainer 包装你的内容
 *    - 选择合适的布局模板（three-column, two-column, single-column, dashboard）
 *    - 使用 Section 组件定义各个区域
 * 
 * 2. 自定义样式：
 *    - 使用 useResponsiveStyles hook 创建响应式样式
 *    - 为不同设备定义不同的样式配置
 *    - 样式会自动根据当前设备应用
 * 
 * 3. 设备检测：
 *    - 使用 useDeviceLayout hook 获取设备信息
 *    - 根据设备类型调整组件行为
 * 
 * 4. 扩展新功能：
 *    - 创建自定义布局配置
 *    - 定义新的样式模板
 *    - 复用现有的响应式逻辑
 * 
 * 5. 最佳实践：
 *    - 优先使用预定义的布局模板
 *    - 只在必要时创建自定义样式
 *    - 保持样式配置的一致性
 *    - 测试不同设备上的表现
 */
