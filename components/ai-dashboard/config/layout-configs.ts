import type { LayoutConfig } from '../components/responsive-container';
import type { ResponsiveStyleConfig } from '../hooks/use-responsive-styles';

/**
 * AI Dashboard 专用布局配置文件
 *
 * 作用：定义AI Dashboard的各种布局模式
 * 包括：全屏模式、普通模式、单列模式等
 *
 * 每个布局配置包含：
 * - template: 布局模板类型
 * - fullscreen: 是否全屏
 * - customStyles: 自定义样式配置
 */
export const AI_DASHBOARD_LAYOUTS = {
  // 工作区布局 - 全屏模式
  // 用于：用户点击全屏按钮后的三栏布局（侧边栏 + 主内容 + 结果区）
  workspaceFullscreen: {
    template: 'three-column' as const,  // 使用三栏模板
    fullscreen: true,                   // 标记为全屏模式
    customStyles: {
      mobile: {
        display: 'flex flex-col',       // 移动端垂直堆叠
        gap: 'gap-4',                   // 间距4
        padding: 'px-4 pb-4 pt-2',      // 内边距
        height: 'h-full min-h-0',       // 全高，最小高度0
      },
      tablet: {
        display: 'grid grid-cols-5',    // 平板5列网格
        gap: 'gap-6',                   // 间距6
        padding: 'px-6 pb-6 pt-4',      // 内边距
        height: 'h-full min-h-0',       // 全高，最小高度0
      },
      desktop: {
        display: 'grid grid-cols-10',   // 桌面10列网格（1:3:6比例）
        gap: 'gap-6',                   // 间距6
        padding: 'px-6 pb-6 pt-4',      // 内边距
        height: 'h-full min-h-0',       // 全高，最小高度0
      },
    },
  } as LayoutConfig,

  // 工作区布局 - 常规模式
  workspaceNormal: {
    template: 'two-column' as const,
    fullscreen: false,
    customStyles: {
      mobile: {
        display: 'flex flex-col',
        gap: 'gap-4',
        padding: 'px-6 pb-12',
        width: 'w-full',
      },
      tablet: {
        display: 'grid grid-cols-5',
        gap: 'gap-6',
        padding: 'px-6 pb-12',
        width: 'w-full',
      },
      desktop: {
        display: 'grid grid-cols-5',
        gap: 'gap-6',
        padding: 'px-6 pb-12',
        width: 'w-full',
        alignItems: 'items-stretch',
      },
    },
  } as LayoutConfig,

  // 生成器布局 - 单列
  generatorSingle: {
    template: 'single-column' as const,
    fullscreen: false,
    customStyles: {
      mobile: {
        display: 'flex flex-col',
        gap: 'gap-6',
        padding: 'p-0',
        width: 'w-full',
      },
      tablet: {
        display: 'flex flex-col',
        gap: 'gap-6',
        padding: 'p-0',
        width: 'w-full',
      },
      desktop: {
        display: 'flex flex-col',
        gap: 'gap-6',
        padding: 'p-0',
        width: 'w-full',
      },
    },
  } as LayoutConfig,
} as const;

// Section 样式配置
export const AI_DASHBOARD_SECTIONS = {
  // 侧边栏 - 模型类型选择器
  sidebar: {
    mobile: {
      width: 'w-full',
      height: 'h-auto',
      order: 'order-1',
      background: 'bg-gradient-to-b from-muted/30 to-muted/10',
      border: 'rounded-2xl border border-border/30',
      padding: 'p-4',
    },
    tablet: {
      width: 'col-span-2',
      height: 'h-auto',
      order: 'order-none',
      background: 'bg-gradient-to-b from-muted/30 to-muted/10',
      border: 'rounded-2xl border border-border/30',
      padding: 'p-4',
    },
    desktop: {
      width: 'col-span-1',
      height: 'h-full',
      order: 'order-none',
      background: 'bg-gradient-to-b from-muted/30 to-muted/10',
      border: 'rounded-2xl border border-border/30',
      padding: 'p-4',
    },
  } as ResponsiveStyleConfig,

  // 主内容区 - 生成器
  main: {
    mobile: {
      width: 'w-full',
      height: 'h-auto',
      order: 'order-2',
      gap: 'space-y-4',
    },
    tablet: {
      width: 'col-span-2',
      height: 'h-auto',
      order: 'order-none',
      gap: 'space-y-4',
    },
    desktop: {
      width: 'col-span-2',
      height: 'h-full',
      order: 'order-none',
      gap: 'space-y-4',
      display: 'flex flex-col',
    },
  } as ResponsiveStyleConfig,

  // 结果区域
  result: {
    mobile: {
      width: 'w-full',
      height: 'h-auto',
      order: 'order-3',
    },
    tablet: {
      width: 'col-span-3',
      height: 'h-auto',
      order: 'order-none',
    },
    desktop: {
      width: 'col-span-3',
      height: 'h-full',
      order: 'order-none',
      display: 'flex flex-col',
    },
  } as ResponsiveStyleConfig,

  // 标签页容器
  tabs: {
    mobile: {
      width: 'w-full',
      margin: 'mb-6',
      background: 'bg-gradient-to-r from-muted/50 to-muted/30',
      border: 'rounded-lg',
    },
    tablet: {
      width: 'w-full',
      margin: 'mb-6',
      background: 'bg-gradient-to-r from-muted/50 to-muted/30',
      border: 'rounded-lg',
    },
    desktop: {
      width: 'w-full',
      margin: 'mb-6',
      background: 'bg-gradient-to-r from-muted/50 to-muted/30',
      border: 'rounded-lg',
    },
  } as ResponsiveStyleConfig,

  // 模型类型按钮
  typeButton: {
    mobile: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-sm font-medium',
      width: 'w-full justify-start',
    },
    tablet: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-base font-medium',
      width: 'w-full justify-start',
    },
    desktop: {
      size: 'h-20 p-3',
      direction: 'flex-col',
      gap: 'gap-2',
      text: 'text-xs font-medium',
      width: 'w-full justify-center',
    },
  } as ResponsiveStyleConfig,
} as const;

// 工具函数：根据全屏状态和设备类型获取布局配置
export function getWorkspaceLayout(isFullscreen: boolean): LayoutConfig {
  return isFullscreen 
    ? AI_DASHBOARD_LAYOUTS.workspaceFullscreen 
    : AI_DASHBOARD_LAYOUTS.workspaceNormal;
}

// 工具函数：获取 Section 样式配置
export function getSectionConfig(sectionName: keyof typeof AI_DASHBOARD_SECTIONS): ResponsiveStyleConfig {
  return AI_DASHBOARD_SECTIONS[sectionName];
}
