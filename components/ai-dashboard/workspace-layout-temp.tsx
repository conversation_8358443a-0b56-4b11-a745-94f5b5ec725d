"use client";

import { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Image,
  Video,
  AlertCircle,
  CheckCircle,
  Maximize,
  Minimize,
  Loader2,
  Eye,
  Download,
  MessageSquare,
  Coins
} from "lucide-react";
import { GeneratorMain } from "./genrator-main";
import { useFullscreen } from "@/hooks/use-fullscreen";


// 导入新的响应式布局框架
import { useDeviceLayout } from "./hooks/use-device-layout";
import { useResponsiveStyles } from "./hooks/use-responsive-styles";

interface GenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

export function WorkspaceLayout() {
  // 从环境变量获取默认模型类型，如果没有设置则默认为image
  const defaultModelType = process.env.NEXT_PUBLIC_DEFAULT_MODEL_TYPE || "image";
  const [activeTab, setActiveTab] = useState(defaultModelType);
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // 当切换tab时清空结果
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setGenerationResult(null);
    setIsGenerating(false);
  };
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  const { isDesktop, isMobile } = useDeviceLayout();
  const moduleRef = useRef<HTMLDivElement>(null);

  // 全屏时的滚动控制
  useEffect(() => {
    if (isFullscreen) {
      // 禁用 body 滚动
      document.body.classList.add('overflow-hidden');
      document.documentElement.classList.add('overflow-hidden');
    } else {
      // 恢复 body 滚动
      document.body.classList.remove('overflow-hidden');
      document.documentElement.classList.remove('overflow-hidden');
    }

    // 组件卸载时清理
    return () => {
      document.body.classList.remove('overflow-hidden');
      document.documentElement.classList.remove('overflow-hidden');
    };
  }, [isFullscreen]);






  // 权限控制：只有登录用户才能看到此组件
  // if (!user) {
  //   return null;
  // }

  const handleFullscreenToggle = () => {
    // 使用CSS全屏，让AIModelModule组件占满浏览器窗口
    console.log('全屏按钮被点击', moduleRef.current);
    if (moduleRef.current) {
      console.log('元素存在，调用toggleFullscreen');
      toggleFullscreen(moduleRef.current);
    } else {
      console.log('元素不存在');
    }
  };

  // 在组件顶层调用所有hooks，避免条件调用
  const { className: typeButtonClassName } = useResponsiveStyles({
    mobile: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-sm font-medium',
      width: 'w-full justify-start',
    },
    tablet: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-base font-medium',
      width: 'w-full justify-start',
    },
    desktop: {
      size: 'h-20 p-3',
      direction: 'flex-col',
      gap: 'gap-2',
      text: 'text-xs font-medium',
      width: 'w-full justify-center',
    },
  });

  // 渲染模型类型选择器（响应式）
  const renderModelTypeSelector = () => {
    const modelTypes = [
      { value: 'text', label: 'TEXT', icon: MessageSquare, color: 'from-blue-500 to-purple-500' },
      { value: 'image', label: 'IMAGE', icon: Image, color: 'from-green-500 to-teal-500' },
      { value: 'video', label: 'VIDEO', icon: Video, color: 'from-orange-500 to-red-500' }
    ];

    return (
      <div className="space-y-3">
        {modelTypes.map(({ value, label, icon: Icon, color }) => (
          <Button
            key={value}
            variant={activeTab === value ? "default" : "ghost"}
            className={`${typeButtonClassName} transition-all duration-200 ${
              activeTab === value
                ? `bg-gradient-to-r ${color} text-white shadow-lg`
                : 'hover:bg-accent/50'
            }`}
            onClick={() => handleTabChange(value)}
          >
            <div className={`p-2 rounded-lg ${activeTab === value ? 'bg-white/20' : 'bg-accent/20'}`}>
              <Icon className="w-4 h-4" />
            </div>
            {(!isDesktop || !isFullscreen) && <span className="font-medium">{label}</span>}
            {isFullscreen && isDesktop && (
              <span className="text-xs font-medium text-center leading-tight">{label.replace('Generate', '')}</span>
            )}
          </Button>
        ))}
      </div>
    );
  };

  // 渲染结果区域
  const renderResult = () => {
    if (!generationResult) {
      return (
        <div className="text-center py-12">
          <div className="p-6 rounded-2xl bg-gradient-to-br from-muted/30 to-muted/10 border border-border/30 max-w-md mx-auto">
            <div className="p-4 rounded-xl bg-gradient-to-r from-primary to-accent mb-4 w-fit mx-auto">
              <Brain className="w-8 h-8 text-primary-foreground" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Start to create</h3>
            <p className="text-muted-foreground text-sm">
              Choose a model and start your creation
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* 生成状态 */}
        <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-muted/30 to-muted/10 border border-border/30">
          <div className={`p-2 rounded-lg ${
            generationResult.status === 'success' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
            generationResult.status === 'failed' ? 'bg-gradient-to-r from-red-500 to-rose-500' :
            'bg-gradient-to-r from-blue-500 to-purple-500'
          }`}>
            {generationResult.status === 'success' && <CheckCircle className="w-5 h-5 text-white" />}
            {generationResult.status === 'failed' && <AlertCircle className="w-5 h-5 text-white" />}
            {(generationResult.status === 'pending' || generationResult.status === 'running') && <Loader2 className="w-5 h-5 animate-spin text-white" />}
          </div>
          <div>
            <span className="font-semibold text-foreground">
              {generationResult.status === 'success' && '生成完成'}
              {generationResult.status === 'failed' && '生成失败'}
              {(generationResult.status === 'pending' || generationResult.status === 'running') && '生成中...'}
            </span>
            {generationResult.progress !== undefined && (
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">进度: {generationResult.progress}%</div>
                <Progress value={generationResult.progress} className="w-full" />
              </div>
            )}
          </div>
        </div>

        {/* 结果内容 */}
        {generationResult.status === 'success' && generationResult.result && (
          <div className="space-y-4">
            {/* 文本结果 */}
            {generationResult.result.text && (
              <div className="p-6 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30 backdrop-blur-sm">
                <pre className="whitespace-pre-wrap text-sm leading-relaxed text-foreground">{generationResult.result.text}</pre>
              </div>
            )}

            {/* 图像结果 */}
            {generationResult.result.images && (
              <div className="grid grid-cols-1 gap-4">
                {generationResult.result.images.map((image, index) => (
                  <div key={index} className="space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30">
                    <img
                      src={image.url}
                      alt={`Generated image ${index + 1}`}
                      className="w-full rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200"
                    />
                    <div className="flex gap-3">
                      <Button size="sm" variant="outline" asChild className="flex-1">
                        <a href={image.url} target="_blank" rel="noopener noreferrer">
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </a>
                      </Button>
                      <Button size="sm" variant="default" asChild className="flex-1">
                        <a href={image.url} download>
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </a>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 视频结果 */}
            {generationResult.result.video && (
              <div className="space-y-2">
                <video
                  src={generationResult.result.video.url}
                  controls
                  className="w-full rounded-lg shadow-md"
                />
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" asChild>
                    <a href={generationResult.result.video.url} target="_blank" rel="noopener noreferrer">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </a>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href={generationResult.result.video.url} download>
                      <Download className="w-4 h-4 mr-1" />
                      Download
                    </a>
                  </Button>
                </div>
              </div>
            )}

            {/* 使用统计 */}
            {generationResult.usage && (
              <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
                <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500">
                  <Coins className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-foreground">
                  Consumed <span className="font-bold text-yellow-600">{generationResult.usage.credits_consumed}</span> credits
                </span>
              </div>
            )}
          </div>
        )}

        {/* 错误信息 */}
        {generationResult.status === 'failed' && generationResult.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to generate: {generationResult.error.detail}
            </AlertDescription>
          </Alert>
        )}

        {/* 进度显示 */}
        {(generationResult.status === 'pending' || generationResult.status === 'running') && (
          <div className="text-center py-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-600">
              Generating... please wait...
              {generationResult.progress !== undefined && ` (${generationResult.progress}%)`}
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      ref={moduleRef}
      className={`
        ${isFullscreen
          ? "fixed inset-0 flex flex-col"
          : "container mx-auto max-w-6xl"
        }
        transition-all duration-300 ease-in-out
      `}
      style={isFullscreen ? {
        zIndex: 40,
        background: 'var(--background)'
      } : undefined}
    >
      {/* 全屏模式下的顶部工具栏 */}
      {isFullscreen && (
        <div className="sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 px-6 py-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 rounded-xl bg-gradient-to-r from-primary to-accent">
                <Brain className="w-6 h-6 text-primary-foreground" />
              </div>
              <h1 className="text-xl font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                AI Workspace - Full Screen
              </h1>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleFullscreenToggle}
              className="flex items-center gap-2 z-50 relative"
            >
              <Minimize className="w-4 h-4" />
              Exit Full
            </Button>
          </div>
        </div>
      )}

      {/* 常规模式下的头部区域 */}
      {!isFullscreen && (
        <div className="px-6 py-12 bg-gradient-to-br from-background/50 via-background to-accent/5 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg">
                <Brain className="w-8 h-8 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  AI Workspace
                </h1>
                <p className="text-muted-foreground mt-1">Unleash Unlimited Creativity</p>
              </div>
            </div>
            <Button
              variant="outline"
              size="lg"
              onClick={handleFullscreenToggle}
              className="flex items-center gap-2"
            >
              <Maximize className="w-5 h-5" />
              Full Screen
            </Button>
          </div>
        </div>
      )}

      {/* 主要内容区域 - 使用响应式容器 */}
      <div className={isFullscreen ? "px-6 pb-6 pt-4 flex-1 min-h-0" : "px-2 md:px-6 pb-12"}>
        <div className={
          isFullscreen
            ? "h-full grid grid-cols-10 gap-6"
            : isMobile
              ? "flex flex-col gap-6"
              : "grid grid-cols-5 gap-6"
        } style={!isFullscreen && !isMobile ? { alignItems: 'stretch', gridAutoRows: '1fr' } : {}}>
          {/* 左栏：模型类型选择器 */}
          {(isFullscreen && isDesktop) && (
            <div className="col-span-1 h-full p-4 bg-gradient-to-b from-muted/30 to-muted/10 rounded-2xl border border-border/30">
              <h3 className="text-sm font-semibold text-foreground mb-4 flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent"></div>
                MODEL TYPE
              </h3>
              {renderModelTypeSelector()}
            </div>
          )}

        {/* 中栏：模型选择和参数设置 */}
        <div className={`${
          isFullscreen
            ? 'col-span-3'
            : isMobile
              ? 'w-full'
              : 'col-span-2'
        } h-full flex flex-col`}>
          {/* 移动端和非全屏桌面端显示水平标签页 */}
          {!(isFullscreen && isDesktop) && (
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-6 bg-gradient-to-r from-muted/50 to-muted/30">
                <TabsTrigger value="text" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white">
                  <MessageSquare className="w-4 h-4" />
                  TEXT LLM
                </TabsTrigger>
                <TabsTrigger value="image" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-teal-500 data-[state=active]:text-white">
                  <Image className="w-4 h-4" />
                  IMAGE
                </TabsTrigger>
                <TabsTrigger value="video" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white">
                  <Video className="w-4 h-4" />
                  VIDEO
                </TabsTrigger>
              </TabsList>
            </Tabs>
          )}

          {/* AI生成器组件 */}
          <GeneratorMain
            modelType={activeTab}
            onResultChange={setGenerationResult}
            onGeneratingChange={setIsGenerating}
          />
        </div>

        {/* 右栏：结果展示 */}
        <div className={`${
          isFullscreen
            ? 'col-span-6 h-full'
            : isMobile
              ? 'w-full mt-6'
              : 'col-span-3 h-full'
        }`}>
          <Card className={`${
            isFullscreen
              ? 'h-full'
              : isMobile
                ? 'min-h-[60vh]'
                : 'h-full'
          } flex flex-col bg-gradient-to-br from-card via-card to-accent/5`}>
            <CardHeader className="border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10 flex-shrink-0">
              <CardTitle className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${
                  activeTab === 'text' ? 'bg-gradient-to-r from-blue-500 to-purple-500' :
                  activeTab === 'image' ? 'bg-gradient-to-r from-green-500 to-teal-500' :
                  'bg-gradient-to-r from-orange-500 to-red-500'
                }`}>
                  {activeTab === 'text' && <MessageSquare className="w-5 h-5 text-white" />}
                  {activeTab === 'image' && <Image className="w-5 h-5 text-white" />}
                  {activeTab === 'video' && <Video className="w-5 h-5 text-white" />}
                </div>
                <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  GENERATION RESULT
                </span>
              </CardTitle>
              <CardDescription className="text-muted-foreground/80">
                {activeTab === 'text' && 'The result of text generation will be displayed here, supporting copy and export'}
                {activeTab === 'image' && 'The result of image generation will be displayed here, supporting preview and download'}
                {activeTab === 'video' && 'The result of video generation will be displayed here, supporting play and download'}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto min-h-0 p-6">
              {renderResult()}
            </CardContent>
          </Card>
        </div>
        </div>
      </div>

    </div>
  );
}
